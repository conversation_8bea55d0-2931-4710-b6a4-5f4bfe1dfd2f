# Mask R-CNN Training Script

This directory contains a standalone Mask R-CNN training script using torchvision's ResNet-50 backbone for object detection on custom COCO format datasets.

## Features

- Uses torchvision's pre-trained Mask R-CNN with ResNet-50 backbone
- Supports custom COCO dataset format
- Object detection only (bounding boxes and masks)
- Automatic mask generation from bounding boxes if segmentation is not available
- Configurable training parameters
- Checkpoint saving and resuming
- Validation support

## Requirements

```bash
pip install torch torchvision opencv-python pillow numpy
```

## Dataset Format

The script expects COCO format JSON annotations with the following structure:

```json
{
  "images": [
    {
      "id": 1,
      "file_name": "image1.jpg",
      "width": 640,
      "height": 480
    }
  ],
  "annotations": [
    {
      "id": 1,
      "image_id": 1,
      "category_id": 1,
      "bbox": [x, y, width, height],
      "area": 1000,
      "iscrowd": 0,
      "segmentation": [[x1, y1, x2, y2, ...]]  # Optional polygon format
    }
  ],
  "categories": [
    {
      "id": 1,
      "name": "object",
      "supercategory": "object"
    }
  ]
}
```

## Usage

### Basic Training

```bash
python train.py \
    --data-root /path/to/images \
    --train-ann /path/to/train_annotations.json \
    --epochs 10 \
    --batch-size 2 \
    --lr 0.005
```

### Training with Validation

```bash
python train.py \
    --data-root /path/to/images \
    --train-ann /path/to/train_annotations.json \
    --val-ann /path/to/val_annotations.json \
    --epochs 20 \
    --batch-size 4 \
    --lr 0.005 \
    --save-dir ./checkpoints \
    --save-freq 5
```

### Resume Training

```bash
python train.py \
    --data-root /path/to/images \
    --train-ann /path/to/train_annotations.json \
    --resume ./checkpoints/checkpoint_epoch_10.pth \
    --epochs 20
```

## Arguments

### Required Arguments
- `--data-root`: Root directory containing images
- `--train-ann`: Path to training annotations JSON file

### Optional Arguments
- `--val-ann`: Path to validation annotations JSON file
- `--epochs`: Number of training epochs (default: 10)
- `--batch-size`: Batch size for training (default: 2)
- `--lr`: Learning rate (default: 0.005)
- `--momentum`: SGD momentum (default: 0.9)
- `--weight-decay`: Weight decay (default: 0.0005)
- `--num-workers`: Number of data loading workers (default: 4)
- `--resume`: Path to checkpoint to resume from
- `--save-dir`: Directory to save checkpoints (default: ./checkpoints)
- `--save-freq`: Save checkpoint every N epochs (default: 5)
- `--device`: Device to use (cuda, cpu, or auto) (default: auto)

## Output

The script saves the following files in the specified save directory:
- `checkpoint_epoch_N.pth`: Regular checkpoints every N epochs
- `best_model.pth`: Best model based on validation loss (if validation set provided)
- `final_model.pth`: Final model after training completion

## Model Architecture

- **Backbone**: ResNet-50 with Feature Pyramid Network (FPN)
- **Pre-trained**: COCO pre-trained weights
- **Head**: Custom classification and mask prediction heads for your dataset
- **Input**: RGB images (automatically resized and normalized)
- **Output**: Bounding boxes, class labels, and segmentation masks

## Notes

- The script automatically handles class mapping from COCO category IDs to 0-indexed labels
- If segmentation annotations are not available, the script creates rectangular masks from bounding boxes
- The model includes background class (class 0), so your custom classes start from index 1
- Learning rate is automatically reduced by factor of 0.1 every 3 epochs
- GPU training is automatically enabled if CUDA is available

## Example Dataset Structure

```
dataset/
├── images/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── train_annotations.json
└── val_annotations.json
```

## Troubleshooting

1. **Out of Memory**: Reduce batch size or use smaller images
2. **No CUDA**: The script will automatically fall back to CPU training
3. **Dataset Loading Issues**: Check that image paths in JSON match actual file locations
4. **Validation Loss Not Improving**: Try reducing learning rate or adding more data augmentation
