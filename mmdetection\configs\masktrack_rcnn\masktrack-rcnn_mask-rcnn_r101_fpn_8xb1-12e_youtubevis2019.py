_base_ = ["./masktrack-rcnn_mask-rcnn_r50_fpn_8xb1-12e_youtubevis2019.py"]
model = dict(
    detector=dict(
        backbone=dict(
            depth=101, init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet101")
        ),
        init_cfg=dict(
            type="Pretrained",
            # noqa: E251
            checkpoint="https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r101_fpn_1x_coco/mask_rcnn_r101_fpn_1x_coco_20200204-1efe0ed5.pth",  # noqa: E501
        ),
    )
)
