Collections:
  - Name: Mask2<PERSON>ormer
    Metadata:
      Training Data: COCO
      Training Techniques:
        - AdamW
        - Weight Decay
      Training Resources: 8x A100 GPUs
      Architecture:
        - Mask2Former
    Paper:
      URL: https://arxiv.org/pdf/2112.01527
      Title: 'Masked-attention Mask Transformer for Universal Image Segmentation'
    README: configs/mask2former/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.23.0/mmdet/models/detectors/mask2former.py#L7
      Version: v2.23.0

Models:
- Name: mask2former_swin-s-p4-w7-224_8xb2-lsj-50e_coco-panoptic
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_swin-s-p4-w7-224_8xb2-lsj-50e_coco-panoptic.py
  Metadata:
    Training Memory (GB): 19.1
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 47.8
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 44.5
  - Task: Panoptic Segmentation
    Dataset: COCO
    Metrics:
      PQ: 54.5
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_swin-s-p4-w7-224_8xb2-lsj-50e_coco-panoptic/mask2former_swin-s-p4-w7-224_8xb2-lsj-50e_coco-panoptic_20220329_225200-4a16ded7.pth
- Name: mask2former_r101_8xb2-lsj-50e_coco
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_r101_8xb2-lsj-50e_coco.py
  Metadata:
    Training Memory (GB): 15.5
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 46.7
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 44.0
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_r101_8xb2-lsj-50e_coco/mask2former_r101_8xb2-lsj-50e_coco_20220426_100250-ecf181e2.pth
- Name: mask2former_r101_8xb2-lsj-50e_coco-panoptic
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_r101_8xb2-lsj-50e_coco-panoptic.py
  Metadata:
    Training Memory (GB): 16.1
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 45.3
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 42.4
  - Task: Panoptic Segmentation
    Dataset: COCO
    Metrics:
      PQ: 52.4
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_r101_8xb2-lsj-50e_coco-panoptic/mask2former_r101_8xb2-lsj-50e_coco-panoptic_20220329_225104-c74d4d71.pth
- Name: mask2former_r50_8xb2-lsj-50e_coco-panoptic
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_r50_8xb2-lsj-50e_coco-panoptic.py
  Metadata:
    Training Memory (GB): 13.9
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 44.5
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 41.8
  - Task: Panoptic Segmentation
    Dataset: COCO
    Metrics:
      PQ: 52.0
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_r50_8xb2-lsj-50e_coco-panoptic/mask2former_r50_8xb2-lsj-50e_coco-panoptic_20230118_125535-54df384a.pth
- Name: mask2former_swin-t-p4-w7-224_8xb2-lsj-50e_coco-panoptic
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_swin-t-p4-w7-224_8xb2-lsj-50e_coco-panoptic.py
  Metadata:
    Training Memory (GB): 15.9
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 46.3
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 43.4
  - Task: Panoptic Segmentation
    Dataset: COCO
    Metrics:
      PQ: 53.4
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_swin-t-p4-w7-224_8xb2-lsj-50e_coco-panoptic/mask2former_swin-t-p4-w7-224_8xb2-lsj-50e_coco-panoptic_20220326_224553-3ec9e0ae.pth
- Name: mask2former_r50_8xb2-lsj-50e_coco
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_r50_8xb2-lsj-50e_coco.py
  Metadata:
    Training Memory (GB): 13.7
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 45.7
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 42.9
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_r50_8xb2-lsj-50e_coco/mask2former_r50_8xb2-lsj-50e_coco_20220506_191028-41b088b6.pth
- Name: mask2former_swin-l-p4-w12-384-in21k_16xb1-lsj-100e_coco-panoptic
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_swin-l-p4-w12-384-in21k_16xb1-lsj-100e_coco-panoptic.py
  Metadata:
    Training Memory (GB): 21.1
    Iterations: 737500
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 52.2
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 48.5
  - Task: Panoptic Segmentation
    Dataset: COCO
    Metrics:
      PQ: 57.6
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_swin-l-p4-w12-384-in21k_16xb1-lsj-100e_coco-panoptic/mask2former_swin-l-p4-w12-384-in21k_16xb1-lsj-100e_coco-panoptic_20220407_104949-82f8d28d.pth
- Name: mask2former_swin-b-p4-w12-384-in21k_8xb2-lsj-50e_coco-panoptic
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_swin-b-p4-w12-384-in21k_8xb2-lsj-50e_coco-panoptic.py
  Metadata:
    Training Memory (GB): 25.8
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 50.0
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 46.3
  - Task: Panoptic Segmentation
    Dataset: COCO
    Metrics:
      PQ: 56.3
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_swin-b-p4-w12-384-in21k_8xb2-lsj-50e_coco-panoptic/mask2former_swin-b-p4-w12-384-in21k_8xb2-lsj-50e_coco-panoptic_20220329_230021-05ec7315.pth
- Name: mask2former_swin-b-p4-w12-384_8xb2-lsj-50e_coco-panoptic
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_swin-b-p4-w12-384_8xb2-lsj-50e_coco-panoptic.py
  Metadata:
    Training Memory (GB): 26.0
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 48.2
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 44.9
  - Task: Panoptic Segmentation
    Dataset: COCO
    Metrics:
      PQ: 55.1
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_swin-b-p4-w12-384_8xb2-lsj-50e_coco-panoptic/mask2former_swin-b-p4-w12-384_8xb2-lsj-50e_coco-panoptic_20220331_002244-8a651d82.pth
- Name: mask2former_swin-t-p4-w7-224_8xb2-lsj-50e_coco
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_swin-t-p4-w7-224_8xb2-lsj-50e_coco.py
  Metadata:
    Training Memory (GB): 15.3
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 47.7
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 44.7
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_swin-t-p4-w7-224_8xb2-lsj-50e_coco/mask2former_swin-t-p4-w7-224_8xb2-lsj-50e_coco_20220508_091649-01b0f990.pth
- Name: mask2former_swin-s-p4-w7-224_8xb2-lsj-50e_coco
  In Collection: Mask2Former
  Config: configs/mask2former/mask2former_swin-s-p4-w7-224_8xb2-lsj-50e_coco.py
  Metadata:
    Training Memory (GB): 18.8
    Iterations: 368750
  Results:
  - Task: Object Detection
    Dataset: COCO
    Metrics:
      box AP: 49.3
  - Task: Instance Segmentation
    Dataset: COCO
    Metrics:
      mask AP: 46.1
  Weights: https://download.openmmlab.com/mmdetection/v3.0/mask2former/mask2former_swin-s-p4-w7-224_8xb2-lsj-50e_coco/mask2former_swin-s-p4-w7-224_8xb2-lsj-50e_coco_20220504_001756-c9d0c4f2.pth
