Collections:
  - Name: DetectoRS
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - ASPP
        - FPN
        - RFP
        - RPN
        - ResNet
        - RoIAlign
        - SAC
    Paper:
      URL: https://arxiv.org/abs/2006.02334
      Title: 'DetectoRS: Detecting Objects with Recursive Feature Pyramid and Switchable Atrous Convolution'
    README: configs/detectors/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.2.0/mmdet/models/backbones/detectors_resnet.py#L205
      Version: v2.2.0

Models:
  - Name: cascade-rcnn_r50-rfp_1x_coco
    In Collection: DetectoRS
    Config: configs/detectors/cascade-rcnn_r50-rfp_1x_coco.py
    Metadata:
      Training Memory (GB): 7.5
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/detectors/cascade_rcnn_r50_rfp_1x_coco/cascade_rcnn_r50_rfp_1x_coco-8cf51bfd.pth

  - Name: cascade-rcnn_r50-sac_1x_coco
    In Collection: DetectoRS
    Config: configs/detectors/cascade-rcnn_r50-sac_1x_coco.py
    Metadata:
      Training Memory (GB): 5.6
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/detectors/cascade_rcnn_r50_sac_1x_coco/cascade_rcnn_r50_sac_1x_coco-24bfda62.pth

  - Name: detectors_cascade-rcnn_r50_1x_coco
    In Collection: DetectoRS
    Config: configs/detectors/detectors_cascade-rcnn_r50_1x_coco.py
    Metadata:
      Training Memory (GB): 9.9
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 47.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/detectors/detectors_cascade_rcnn_r50_1x_coco/detectors_cascade_rcnn_r50_1x_coco-32a10ba0.pth

  - Name: htc_r50-rfp_1x_coco
    In Collection: DetectoRS
    Config: configs/detectors/htc_r50-rfp_1x_coco.py
    Metadata:
      Training Memory (GB): 11.2
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 46.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  40.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/detectors/htc_r50_rfp_1x_coco/htc_r50_rfp_1x_coco-8ff87c51.pth

  - Name: htc_r50-sac_1x_coco
    In Collection: DetectoRS
    Config: configs/detectors/htc_r50-sac_1x_coco.py
    Metadata:
      Training Memory (GB): 9.3
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 46.4
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  40.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/detectors/htc_r50_sac_1x_coco/htc_r50_sac_1x_coco-bfa60c54.pth

  - Name: detectors_htc-r50_1x_coco
    In Collection: DetectoRS
    Config: configs/detectors/detectors_htc-r50_1x_coco.py
    Metadata:
      Training Memory (GB): 13.6
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 49.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP:  42.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/detectors/detectors_htc_r50_1x_coco/detectors_htc_r50_1x_coco-329b1453.pth
