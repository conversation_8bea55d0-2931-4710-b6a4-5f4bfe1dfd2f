_base_ = ["./ld_r18-gflv1-r101_fpn_1x_coco.py"]
teacher_ckpt = "https://download.openmmlab.com/mmdetection/v2.0/gfl/gfl_r101_fpn_dconv_c3-c5_mstrain_2x_coco/gfl_r101_fpn_dconv_c3-c5_mstrain_2x_coco_20200630_102002-134b07df.pth"  # noqa
model = dict(
    teacher_config="configs/gfl/gfl_r101-dconv-c3-c5_fpn_ms-2x_coco.py",
    teacher_ckpt=teacher_ckpt,
    backbone=dict(
        type="ResNet",
        depth=101,
        num_stages=4,
        out_indices=(0, 1, 2, 3),
        frozen_stages=1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        init_cfg=dict(type="Pretrained", checkpoint="torchvision://resnet101"),
    ),
    neck=dict(
        type="FPN",
        in_channels=[256, 512, 1024, 2048],
        out_channels=256,
        start_level=1,
        add_extra_convs="on_output",
        num_outs=5,
    ),
)

max_epochs = 24
param_scheduler = [
    dict(type="LinearLR", start_factor=0.001, by_epoch=False, begin=0, end=500),
    dict(
        type="MultiStepLR", begin=0, end=max_epochs, by_epoch=True, milestones=[16, 22], gamma=0.1
    ),
]
train_cfg = dict(max_epochs=max_epochs)

# multi-scale training
train_pipeline = [
    dict(type="LoadImageFromFile", backend_args={{_base_.backend_args}}),
    dict(type="LoadAnnotations", with_bbox=True),
    dict(type="RandomResize", scale=[(1333, 480), (1333, 800)], keep_ratio=True),
    dict(type="RandomFlip", prob=0.5),
    dict(type="PackDetInputs"),
]
train_dataloader = dict(dataset=dict(pipeline=train_pipeline))
