#!/usr/bin/env python3
"""
Test script to verify Mask R-CNN setup and dependencies.
"""

import sys
import torch
import torchvision
import numpy as np
from PIL import Image
import cv2


def test_imports():
    """Test if all required packages can be imported."""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
    except ImportError:
        print("✗ PyTorch not found")
        return False
    
    try:
        import torchvision
        print(f"✓ Torchvision {torchvision.__version__}")
    except ImportError:
        print("✗ Torchvision not found")
        return False
    
    try:
        import cv2
        print(f"✓ OpenCV {cv2.__version__}")
    except ImportError:
        print("✗ OpenCV not found")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError:
        print("✗ NumPy not found")
        return False
    
    try:
        from PIL import Image
        print(f"✓ Pillow {Image.__version__}")
    except ImportError:
        print("✗ Pillow not found")
        return False
    
    return True


def test_cuda():
    """Test CUDA availability."""
    print("\nTesting CUDA...")
    
    if torch.cuda.is_available():
        print(f"✓ CUDA available")
        print(f"  Device count: {torch.cuda.device_count()}")
        print(f"  Current device: {torch.cuda.current_device()}")
        print(f"  Device name: {torch.cuda.get_device_name()}")
        return True
    else:
        print("⚠ CUDA not available (will use CPU)")
        return False


def test_model_loading():
    """Test if Mask R-CNN model can be loaded."""
    print("\nTesting model loading...")
    
    try:
        from torchvision.models.detection import maskrcnn_resnet50_fpn
        from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
        from torchvision.models.detection.mask_rcnn import MaskRCNNPredictor
        
        # Load pre-trained model
        model = maskrcnn_resnet50_fpn(pretrained=True)
        print("✓ Pre-trained Mask R-CNN loaded")
        
        # Test model modification
        num_classes = 2  # background + 1 class
        in_features = model.roi_heads.box_predictor.cls_score.in_features
        model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
        
        in_features_mask = model.roi_heads.mask_predictor.conv5_mask.in_channels
        hidden_layer = 256
        model.roi_heads.mask_predictor = MaskRCNNPredictor(
            in_features_mask, hidden_layer, num_classes
        )
        print("✓ Model heads modified successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        return False


def test_dummy_inference():
    """Test inference with dummy data."""
    print("\nTesting dummy inference...")
    
    try:
        from torchvision.models.detection import maskrcnn_resnet50_fpn
        from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
        from torchvision.models.detection.mask_rcnn import MaskRCNNPredictor
        
        # Create model
        model = maskrcnn_resnet50_fpn(pretrained=True)
        num_classes = 2
        in_features = model.roi_heads.box_predictor.cls_score.in_features
        model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
        
        in_features_mask = model.roi_heads.mask_predictor.conv5_mask.in_channels
        hidden_layer = 256
        model.roi_heads.mask_predictor = MaskRCNNPredictor(
            in_features_mask, hidden_layer, num_classes
        )
        
        model.eval()
        
        # Create dummy input
        dummy_input = torch.randn(1, 3, 224, 224)
        
        # Test inference
        with torch.no_grad():
            output = model(dummy_input)
        
        print("✓ Dummy inference successful")
        print(f"  Output keys: {list(output[0].keys())}")
        print(f"  Boxes shape: {output[0]['boxes'].shape}")
        print(f"  Labels shape: {output[0]['labels'].shape}")
        print(f"  Scores shape: {output[0]['scores'].shape}")
        print(f"  Masks shape: {output[0]['masks'].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Dummy inference failed: {e}")
        return False


def test_dataset_loading():
    """Test dataset loading functionality."""
    print("\nTesting dataset components...")
    
    try:
        # Test JSON loading
        import json
        dummy_coco = {
            "images": [{"id": 1, "file_name": "test.jpg", "width": 640, "height": 480}],
            "annotations": [{"id": 1, "image_id": 1, "category_id": 1, "bbox": [10, 10, 100, 100], "area": 10000, "iscrowd": 0}],
            "categories": [{"id": 1, "name": "test", "supercategory": "object"}]
        }
        
        # Test JSON serialization
        json_str = json.dumps(dummy_coco)
        loaded_coco = json.loads(json_str)
        print("✓ JSON handling works")
        
        # Test image creation
        dummy_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        pil_image = Image.fromarray(dummy_image)
        print("✓ Image handling works")
        
        # Test transforms
        from torchvision import transforms
        transform = transforms.Compose([transforms.ToTensor()])
        tensor_image = transform(pil_image)
        print(f"✓ Transforms work - tensor shape: {tensor_image.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Dataset loading test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("Mask R-CNN Setup Test")
    print("=" * 30)
    
    tests = [
        ("Imports", test_imports),
        ("CUDA", test_cuda),
        ("Model Loading", test_model_loading),
        ("Dummy Inference", test_dummy_inference),
        ("Dataset Components", test_dataset_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 30)
    print("Test Summary:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your setup is ready for training.")
    else:
        print(f"\n⚠ {len(results) - passed} test(s) failed. Please check your installation.")
        print("\nTo install missing dependencies:")
        print("pip install torch torchvision opencv-python pillow numpy")


if __name__ == "__main__":
    main()
