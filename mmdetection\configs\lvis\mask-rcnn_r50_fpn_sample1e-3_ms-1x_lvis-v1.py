_base_ = [
    "../_base_/models/mask-rcnn_r50_fpn.py",
    "../_base_/datasets/lvis_v1_instance.py",
    "../_base_/schedules/schedule_1x.py",
    "../_base_/default_runtime.py",
]
model = dict(
    roi_head=dict(bbox_head=dict(num_classes=1203), mask_head=dict(num_classes=1203)),
    test_cfg=dict(
        rcnn=dict(
            score_thr=0.0001,
            # LVIS allows up to 300
            max_per_img=300,
        )
    ),
)
