{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": 1,
   "id": "10fa511c",
   "metadata": {},
   "outputs": [],
   "source": [
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "from sklearn.metrics import root_mean_squared_error, r2_score\n",
    "\n",
    "plt.style.use('ggplot') # 应用主题\n",
    "# 设置全局字体为Times New Roman\n",
    "plt.rcParams['font.family'] = 'serif'\n",
    "plt.rcParams['font.serif'] = ['Times New Roman']"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 2,
   "id": "60daf922",
   "metadata": {},
   "outputs": [],
   "source": [
    "# 读取数据\n",
    "df = pd.read_csv(r\"C:\projects\\tobacco\\tobacco_det_data\\batch2_384\\approach\\mine_kmeans\\results.csv\")\n",
    "\n",
    "# 提取数据\n",
    "reference_tobaccos = df[\"n_ground_truth\"]\n",
    "detected_tp = df[\"n_matched\"]\n",
    "detected_tp_fp = df[\"n_inference\"]"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 3,
   "id": "695915a4",
   "metadata": {},
   "outputs": [],
   "source": [
    "# 回归与误差计算\n",
    "r2_tp_fp = r2_score(reference_tobaccos, detected_tp_fp)\n",
    "rmse_tp_fp = root_mean_squared_error(reference_tobaccos, detected_tp_fp)\n",
    "\n",
    "r2_tp = r2_score(reference_tobaccos, detected_tp)\n",
    "rmse_tp = root_mean_squared_error(reference_tobaccos, detected_tp)\n",
    "\n",
    "# 分类指标平均值\n",
    "mean_precision = df[\"precision\"].mean()\n",
    "mean_recall = df[\"recall\"].mean()\n",
    "mean_f1 = df[\"f1_score\"].mean()\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 4,
   "id": "fbda566b",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "image/png": "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",
      "text/plain": [
       "<Figure size 600x600 with 1 Axes>"
      ]
     },
     "metadata": {},
     "output_type": "display_data"
    }
   ],
   "source": [
    "# 图形设置\n",
    "fig, ax = plt.subplots(figsize=(6, 6))\n",
    "ax.set_facecolor(\"#eaeaf2\")\n",
    "\n",
    "# 计算坐标轴范围并截断左侧空白\n",
    "min_ref = reference_tobaccos.min()\n",
    "max_ref = reference_tobaccos.max()\n",
    "min_det = min(detected_tp.min(), detected_tp_fp.min())\n",
    "max_det = max(detected_tp.max(), detected_tp_fp.max())\n",
    "\n",
    "axis_min = min(min_ref, min_det) - 50\n",
    "axis_max = max(max_ref, max_det) + 50\n",
    "\n",
    "axis_min = 200\n",
    "axis_max = 1300\n",
    "\n",
    "# 绘制对角虚线 y=x，确保在背景层可见\n",
    "ax.plot([axis_min, axis_max], [axis_min, axis_max], \"k--\", alpha=0.5, zorder=0)\n",
    "\n",
    "# 散点图\n",
    "ax.scatter(reference_tobaccos, detected_tp, color=\"orange\", label=\"TP\", zorder=2, marker=\"x\")\n",
    "ax.scatter(reference_tobaccos, detected_tp_fp, color=\"midnightblue\", label=\"TP+FP\", zorder=2, marker=\"o\")\n",
    "\n",
    "# 回归线\n",
    "m_tp_fp, b_tp_fp = np.polyfit(reference_tobaccos, detected_tp_fp, 1)\n",
    "m_tp, b_tp = np.polyfit(reference_tobaccos, detected_tp, 1)\n",
    "ax.plot(reference_tobaccos, m_tp_fp * reference_tobaccos + b_tp_fp, color=\"midnightblue\", zorder=1)\n",
    "ax.plot(reference_tobaccos, m_tp * reference_tobaccos + b_tp, color=\"orange\", zorder=1)\n",
    "\n",
    "# 坐标轴标签与范围\n",
    "ax.set_xlabel(\"Reference tobaccos\", fontsize=12)\n",
    "ax.set_ylabel(\"Detected tobaccos\", fontsize=12)\n",
    "ax.set_xlim(axis_min, axis_max)\n",
    "ax.set_ylim(axis_min, axis_max)\n",
    "\n",
    "# 图例\n",
    "ax.legend(loc=\"lower right\")\n",
    "\n",
    "# 注释框\n",
    "info_text = (\n",
    "    f\"TP+FP: R²={r2_tp_fp:.2f}, RMSE={rmse_tp_fp:.2f}\\n\"\n",
    "    f\"TP:    R²={r2_tp:.2f}, RMSE={rmse_tp:.2f}\\n\"\n",
    "    f\"P={mean_precision:.3f}, R={mean_recall:.3f}, F1={mean_f1:.3f}\"\n",
    ")\n",
    "bbox_props = dict(boxstyle=\"round,pad=0.4\", fc=\"white\", ec=\"black\", lw=0.5)\n",
    "ax.text(\n",
    "    0.05,\n",
    "    0.85,\n",
    "    info_text,\n",
    "    transform=ax.transAxes,\n",
    "    fontsize=11,\n",
    "    verticalalignment=\"top\",\n",
    "    bbox=bbox_props,\n",
    "    fontdict={\"font\": \"Consolas\"},\n",
    ")\n",
    "\n",
    "# 左上角参数标签框\n",
    "ax.text(\n",
    "    0.05,\n",
    "    0.95,\n",
    "    \"KMeans\",\n",
    "    transform=ax.transAxes,\n",
    "    fontsize=12,\n",
    "    verticalalignment=\"top\",\n",
    "    bbox=dict(boxstyle=\"round,pad=0.3\", fc=\"lavender\", ec=\"none\"),\n",
    ")\n",
    "\n",
    "# 布局与展示\n",
    "plt.tight_layout()\n",
    "plt.savefig(r\"C:\\Users\\<USER>\\Desktop\\images\\KMeans.pdf\")\n",
    "plt.show()"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "openmmlab",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.10.17"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
