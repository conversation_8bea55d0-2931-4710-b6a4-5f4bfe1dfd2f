_base_ = "./htc_r50_fpn_1x_coco.py"
model = dict(
    backbone=dict(
        type="ResNeXt",
        depth=101,
        groups=32,
        base_width=4,
        num_stages=4,
        out_indices=(0, 1, 2, 3),
        frozen_stages=1,
        norm_cfg=dict(type="BN", requires_grad=True),
        norm_eval=True,
        style="pytorch",
        init_cfg=dict(type="Pretrained", checkpoint="open-mmlab://resnext101_32x4d"),
    )
)

train_dataloader = dict(batch_size=1, num_workers=1)

# learning policy
max_epochs = 20
param_scheduler = [
    dict(type="LinearLR", start_factor=0.001, by_epoch=False, begin=0, end=500),
    dict(
        type="MultiStepLR", begin=0, end=max_epochs, by_epoch=True, milestones=[16, 19], gamma=0.1
    ),
]
train_cfg = dict(max_epochs=max_epochs)
