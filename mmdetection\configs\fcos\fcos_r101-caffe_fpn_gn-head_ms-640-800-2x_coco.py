_base_ = "./fcos_r50-caffe_fpn_gn-head_1x_coco.py"

# model settings
model = dict(
    backbone=dict(
        depth=101,
        init_cfg=dict(type="Pretrained", checkpoint="open-mmlab://detectron/resnet101_caffe"),
    )
)

# dataset settings
train_pipeline = [
    dict(type="LoadImageFromFile", backend_args={{_base_.backend_args}}),
    dict(type="LoadAnnotations", with_bbox=True),
    dict(type="RandomChoiceResize", scales=[(1333, 640), (1333, 800)], keep_ratio=True),
    dict(type="RandomFlip", prob=0.5),
    dict(type="PackDetInputs"),
]
train_dataloader = dict(dataset=dict(pipeline=train_pipeline))

# training schedule for 2x
max_epochs = 24
train_cfg = dict(max_epochs=max_epochs)

# learning rate
param_scheduler = [
    dict(type="ConstantLR", factor=1.0 / 3, by_epoch=False, begin=0, end=500),
    dict(
        type="MultiStepLR", begin=0, end=max_epochs, by_epoch=True, milestones=[16, 22], gamma=0.1
    ),
]
