albu_example/mask-rcnn_r50_fpn_albu_1x_coco.py
atss/atss_r50_fpn_1x_coco.py
autoassign/autoassign_r50-caffe_fpn_1x_coco.py
boxinst/boxinst_r50_fpn_ms-90k_coco.py
carafe/faster-rcnn_r50_fpn-carafe_1x_coco.py
cascade_rcnn/cascade-rcnn_r50_fpn_1x_coco.py
cascade_rcnn/cascade-mask-rcnn_r50_fpn_1x_coco.py
cascade_rpn/cascade-rpn_faster-rcnn_r50-caffe_fpn_1x_coco.py
centernet/centernet-update_r50-caffe_fpn_ms-1x_coco.py
centripetalnet/centripetalnet_hourglass104_16xb6-crop511-210e-mstest_coco.py
condinst/condinst_r50_fpn_ms-poly-90k_coco_instance.py
conditional_detr/conditional-detr_r50_8xb2-50e_coco.py
convnext/mask-rcnn_convnext-t-p4-w7_fpn_amp-ms-crop-3x_coco.py
cornernet/cornernet_hourglass104_8xb6-210e-mstest_coco.py
dab_detr/dab-detr_r50_8xb2-50e_coco.py
dcn/mask-rcnn_r50-dconv-c3-c5_fpn_1x_coco.py
dcnv2/faster-rcnn_r50_fpn_mdpool_1x_coco.py
ddod/ddod_r50_fpn_1x_coco.py
deformable_detr/deformable-detr_r50_16xb2-50e_coco.py
detectors/detectors_htc-r50_1x_coco.py
detr/detr_r50_8xb2-150e_coco.py
dino/dino-4scale_r50_8xb2-12e_coco.py
double_heads/dh-faster-rcnn_r50_fpn_1x_coco.py
dyhead/atss_r50_fpn_dyhead_1x_coco.py
dynamic_rcnn/dynamic-rcnn_r50_fpn_1x_coco.py
efficientnet/retinanet_effb3_fpn_8xb4-crop896-1x_coco.py
empirical_attention/faster-rcnn_r50-attn0010-dcn_fpn_1x_coco.py
faster_rcnn/faster-rcnn_r50_fpn_1x_coco.py
fcos/fcos_r50-caffe_fpn_gn-head-center-normbbox-centeronreg-giou_1x_coco.py
foveabox/fovea_r50_fpn_gn-head-align_4xb4-2x_coco.py
fpg/retinanet_r50_fpg_crop640_50e_coco.py
free_anchor/freeanchor_r50_fpn_1x_coco.py
fsaf/fsaf_r50_fpn_1x_coco.py
gcnet/mask-rcnn_r50-gcb-r4-c3-c5_fpn_1x_coco.py
gfl/gfl_r50_fpn_1x_coco.py
glip/glip_atss_swin-t_a_fpn_dyhead_pretrain_obj365.py
ghm/retinanet_r50_fpn_ghm-1x_coco.py
gn/mask-rcnn_r50_fpn_gn-all_2x_coco.py
gn+ws/faster-rcnn_r50_fpn_gn-ws-all_1x_coco.py
grid_rcnn/grid-rcnn_r50_fpn_gn-head_2x_coco.py
groie/faste-rcnn_r50_fpn_groie_1x_coco.py
guided_anchoring/ga-faster-rcnn_r50-caffe_fpn_1x_coco.py
hrnet/htc_hrnetv2p-w18_20e_coco.py
htc/htc_r50_fpn_1x_coco.py
instaboost/mask-rcnn_r50_fpn_instaboost-4x_coco.py
lad/lad_r50-paa-r101_fpn_2xb8_coco_1x.py
ld/ld_r18-gflv1-r101_fpn_1x_coco.py
libra_rcnn/libra-faster-rcnn_r50_fpn_1x_coco.py
lvis/mask-rcnn_r50_fpn_sample1e-3_ms-1x_lvis-v1.py
mask2former/mask2former_r50_8xb2-lsj-50e_coco.py
mask2former/mask2former_r50_8xb2-lsj-50e_coco-panoptic.py
mask_rcnn/mask-rcnn_r50_fpn_1x_coco.py
maskformer/maskformer_r50_ms-16xb1-75e_coco.py
ms_rcnn/ms-rcnn_r50-caffe_fpn_1x_coco.py
nas_fcos/nas-fcos_r50-caffe_fpn_nashead-gn-head_4xb4-1x_coco.py
nas_fpn/retinanet_r50_nasfpn_crop640-50e_coco.py
paa/paa_r50_fpn_1x_coco.py
pafpn/faster-rcnn_r50_pafpn_1x_coco.py
panoptic_fpn/panoptic-fpn_r50_fpn_1x_coco.py
pisa/faster-rcnn_r50_fpn_pisa_1x_coco.py
point_rend/point-rend_r50-caffe_fpn_ms-1x_coco.py
pvt/retinanet_pvtv2-b0_fpn_1x_coco.py
queryinst/queryinst_r50_fpn_1x_coco.py
regnet/mask-rcnn_regnetx-3.2GF_fpn_1x_coco.py
reppoints/reppoints-moment_r50_fpn-gn_head-gn_1x_coco.py
res2net/faster-rcnn_res2net-101_fpn_2x_coco.py
resnest/mask-rcnn_s50_fpn_syncbn-backbone+head_ms-1x_coco.py
resnet_strikes_back/faster-rcnn_r50-rsb-pre_fpn_1x_coco.py
retinanet/retinanet_r50_fpn_1x_coco.py
rpn/rpn_r50_fpn_1x_coco.py
rtmdet/rtmdet_s_8xb32-300e_coco.py
rtmdet/rtmdet-ins_s_8xb32-300e_coco.py
sabl/sabl-retinanet_r50_fpn_1x_coco.py
scnet/scnet_r50_fpn_1x_coco.py
scratch/faster-rcnn_r50-scratch_fpn_gn-all_6x_coco.py
seesaw_loss/mask-rcnn_r50_fpn_seesaw-loss_random-ms-2x_lvis-v1.py
simple_copy_paste/mask-rcnn_r50_fpn_rpn-2conv_4conv1fc_syncbn-all_32xb2-ssj-scp-90k_coco.py
soft_teacher/soft-teacher_faster-rcnn_r50-caffe_fpn_180k_semi-0.1-coco.py
solo/solo_r50_fpn_1x_coco.py
solov2/solov2_r50_fpn_1x_coco.py
sparse_rcnn/sparse-rcnn_r50_fpn_1x_coco.py
ssd/ssd300_coco.py
swin/mask-rcnn_swin-t-p4-w7_fpn_1x_coco.py
tood/tood_r50_fpn_1x_coco.py
tridentnet/tridentnet_r50-caffe_1x_coco.py
vfnet/vfnet_r50_fpn_1x_coco.py
yolact/yolact_r50_8xb8-55e_coco.py
yolo/yolov3_d53_8xb8-320-273e_coco.py
yolof/yolof_r50-c5_8xb8-1x_coco.py
yolox/yolox_s_8xb8-300e_coco.py
deepsort/deepsort_faster-rcnn_r50_fpn_8xb2-4e_mot17halftrain_test-mot17halfval.py
mask2former_vis/mask2former_r50_8xb2-8e_youtubevis2021.py
masktrack_rcnn/masktrack-rcnn_mask-rcnn_r50_fpn_8xb1-12e_youtubevis2021.py
ocsort/ocsort_yolox_x_8xb4-amp-80e_crowdhuman-mot17halftrain_test-mot17halfval.py
qdtrack/qdtrack_faster-rcnn_r50_fpn_8xb2-4e_mot17halftrain_test-mot17halfval.py
strongsort/strongsort_yolox_x_8xb4-80e_crowdhuman-mot17halftrain_test-mot17halfval.py
