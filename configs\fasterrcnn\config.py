_base_ = [
    "../../mmdetection/configs/_base_/models/faster-rcnn_r50_fpn.py",
    "../base/default_runtime.py",
    "../base/data.py",
    "../base/schedule.py",
]

# model settings
model = dict(
    roi_head=dict(
        bbox_head=dict(
            num_classes=1,
        ),
    ),
    # model training and testing settings
    train_cfg=dict(
        rpn_proposal=dict(
            nms_pre=2000, max_per_img=1500, nms=dict(type="nms", iou_threshold=0.3), min_bbox_size=0
        ),
    ),
    test_cfg=dict(
        rpn=dict(
            nms_pre=2000, max_per_img=1500, nms=dict(type="nms", iou_threshold=0.3), min_bbox_size=0
        ),
        rcnn=dict(score_thr=0.05, nms=dict(type="nms", iou_threshold=0.5), max_per_img=1500),
        # soft-nms is also supported for rcnn testing
        # e.g., nms=dict(type='soft_nms', iou_threshold=0.5, min_score=0.05)
    ),
)
