_base_ = "./ga-retinanet_r101-caffe_fpn_1x_coco.py"

train_pipeline = [
    dict(type="LoadImageFromFile", backend_args={{_base_.backend_args}}),
    dict(type="LoadAnnotations", with_bbox=True),
    dict(type="RandomResize", scale=[(1333, 480), (1333, 960)], keep_ratio=True),
    dict(type="RandomFlip", prob=0.5),
    dict(type="PackDetInputs"),
]
train_dataloader = dict(dataset=dict(pipeline=train_pipeline))

# learning policy
max_epochs = 24
train_cfg = dict(type="EpochBasedTrainLoop", max_epochs=max_epochs, val_interval=1)

# learning rate
param_scheduler = [
    dict(type="LinearLR", start_factor=1.0 / 3.0, by_epoch=False, begin=0, end=500),
    dict(
        type="MultiStepLR", begin=0, end=max_epochs, by_epoch=True, milestones=[16, 22], gamma=0.1
    ),
]
