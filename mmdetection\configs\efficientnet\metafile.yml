Models:
  - Name: retinanet_effb3_fpn_8xb4-crop896-1x_coco
    In Collection: RetinaNet
    Config: configs/efficientnet/retinanet_effb3_fpn_8xb4-crop896-1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/efficientnet/retinanet_effb3_fpn_crop896_8x4_1x_coco/retinanet_effb3_fpn_crop896_8x4_1x_coco_20220322_234806-615a0dda.pth
    Paper:
      URL: https://arxiv.org/abs/1905.11946v5
      Title: 'EfficientNet: Rethinking Model Scaling for Convolutional Neural Networks'
    README: configs/efficientnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.23.0/mmdet/models/backbones/efficientnet.py#L159
      Version: v2.23.0
