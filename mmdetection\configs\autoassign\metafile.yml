Collections:
  - Name: AutoAssign
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - AutoAssign
        - FPN
        - ResNet
    Paper:
      URL: https://arxiv.org/abs/2007.03496
      Title: 'AutoAssign: Differentiable Label Assignment for Dense Object Detection'
    README: configs/autoassign/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.12.0/mmdet/models/detectors/autoassign.py#L6
      Version: v2.12.0

Models:
  - Name: autoassign_r50-caffe_fpn_1x_coco
    In Collection: AutoAssign
    Config: configs/autoassign/autoassign_r50-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.08
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/autoassign/auto_assign_r50_fpn_1x_coco/auto_assign_r50_fpn_1x_coco_20210413_115540-5e17991f.pth
