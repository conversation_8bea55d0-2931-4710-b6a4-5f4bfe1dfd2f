#!/usr/bin/env python3
"""
Mask R-CNN Inference Script
Load trained model and perform inference on images.
"""

import argparse
import json
import os
from pathlib import Path

import cv2
import numpy as np
import torch
import torchvision.transforms as transforms
from PIL import Image
from torchvision.models.detection import maskrcnn_resnet50_fpn
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
from torchvision.models.detection.mask_rcnn import MaskRCNNPredictor


def get_model(num_classes):
    """Create Mask R-CNN model with ResNet-50 backbone."""
    # Load pre-trained model
    model = maskrcnn_resnet50_fpn(weights='DEFAULT')
    
    # Get number of input features for the classifier
    in_features = model.roi_heads.box_predictor.cls_score.in_features
    
    # Replace the pre-trained head with a new one
    model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
    
    # Get number of input features for the mask classifier
    in_features_mask = model.roi_heads.mask_predictor.conv5_mask.in_channels
    hidden_layer = 256
    
    # Replace the mask predictor with a new one
    model.roi_heads.mask_predictor = MaskRCNNPredictor(
        in_features_mask, hidden_layer, num_classes
    )
    
    return model


def load_model(checkpoint_path, num_classes, device):
    """Load trained model from checkpoint."""
    model = get_model(num_classes)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()
    
    print(f"Model loaded from: {checkpoint_path}")
    print(f"Epoch: {checkpoint['epoch']}, Loss: {checkpoint['loss']:.4f}")
    
    return model


def preprocess_image(image_path):
    """Preprocess image for inference."""
    image = Image.open(image_path).convert('RGB')
    transform = transforms.Compose([
        transforms.ToTensor()
    ])
    return transform(image), image


def postprocess_predictions(predictions, confidence_threshold=0.5):
    """Filter predictions by confidence threshold."""
    boxes = predictions['boxes'].cpu().numpy()
    labels = predictions['labels'].cpu().numpy()
    scores = predictions['scores'].cpu().numpy()
    masks = predictions['masks'].cpu().numpy()
    
    # Filter by confidence
    keep = scores >= confidence_threshold
    
    return {
        'boxes': boxes[keep],
        'labels': labels[keep],
        'scores': scores[keep],
        'masks': masks[keep]
    }


def visualize_predictions(image, predictions, class_names, output_path=None):
    """Visualize predictions on image."""
    # Convert PIL image to numpy array
    if isinstance(image, Image.Image):
        image_np = np.array(image)
    else:
        image_np = image.copy()
    
    # Draw bounding boxes and masks
    for i, (box, label, score, mask) in enumerate(zip(
        predictions['boxes'],
        predictions['labels'], 
        predictions['scores'],
        predictions['masks']
    )):
        # Draw bounding box
        x1, y1, x2, y2 = box.astype(int)
        cv2.rectangle(image_np, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # Draw label and score
        class_name = class_names.get(label, f'Class {label}')
        label_text = f'{class_name}: {score:.2f}'
        cv2.putText(image_np, label_text, (x1, y1 - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # Draw mask
        mask_binary = (mask[0] > 0.5).astype(np.uint8)
        colored_mask = np.zeros_like(image_np)
        colored_mask[:, :, 1] = mask_binary * 255  # Green channel
        
        # Blend mask with image
        alpha = 0.3
        image_np = cv2.addWeighted(image_np, 1 - alpha, colored_mask, alpha, 0)
    
    if output_path:
        cv2.imwrite(output_path, cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR))
        print(f"Visualization saved: {output_path}")
    
    return image_np


def load_class_names(annotation_file):
    """Load class names from COCO annotation file."""
    with open(annotation_file, 'r') as f:
        coco_data = json.load(f)
    
    class_names = {0: 'background'}  # Background class
    for cat in coco_data['categories']:
        # Map to 1-indexed labels (0 is background)
        class_names[cat['id']] = cat['name']
    
    return class_names


def main():
    """Main inference function."""
    parser = argparse.ArgumentParser(description='Mask R-CNN Inference')
    
    # Model arguments
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to trained model checkpoint')
    # Input arguments
    parser.add_argument('--image', type=str, required=True,
                        help='Path to input image')
    parser.add_argument('--output', type=str, default=None,
                        help='Path to save output image')
    
    # Inference arguments
    parser.add_argument('--confidence', type=float, default=0.3,
                        help='Confidence threshold for predictions')
    parser.add_argument('--device', type=str, default='auto',
                        help='Device to use (cuda, cpu, or auto)')
    
    args = parser.parse_args()
    
    # Set device
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    print(f'Using device: {device}')
    
    # Load model
    model = load_model(args.checkpoint, 2, device)
    
    # Load class names
    class_names = {}
    for i in range(2):
        class_names[i] = f'Class {i}' if i > 0 else 'background'
    
    # Load and preprocess image
    print(f"Processing image: {args.image}")
    image_tensor, original_image = preprocess_image(args.image)
    image_tensor = image_tensor.unsqueeze(0).to(device)  # Add batch dimension
    
    # Perform inference
    with torch.no_grad():
        predictions = model(image_tensor)[0]  # Get first (and only) prediction
    
    # Postprocess predictions
    filtered_predictions = predictions
    # filtered_predictions = postprocess_predictions(predictions, args.confidence)
    
    print(f"Found {len(filtered_predictions['boxes'])} detections")
    
    # Print predictions
    for i, (box, label, score) in enumerate(zip(
        filtered_predictions['boxes'],
        filtered_predictions['labels'],
        filtered_predictions['scores']
    )):
        class_name = class_names.get(label, f'Class {label}')
        print(f"Detection {i+1}: {class_name} ({score:.3f}) at {box}")
    
    # Visualize results
    if args.output or len(filtered_predictions['boxes']) > 0:
        output_path = args.output or f"output_{Path(args.image).stem}.jpg"
        visualized = visualize_predictions(
            original_image, filtered_predictions, class_names, output_path
        )
        
        if not args.output:
            print(f"Visualization saved: {output_path}")


if __name__ == '__main__':
    main()
