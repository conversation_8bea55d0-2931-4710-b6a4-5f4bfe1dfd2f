Collections:
  - Name: Deformable Convolutional Networks v2
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Deformable Convolution
    Paper:
      URL: https://arxiv.org/abs/1811.11168
      Title: "Deformable ConvNets v2: More Deformable, Better Results"
    README: configs/dcnv2/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/ops/dcn/deform_conv.py#L15
      Version: v2.0.0

Models:
  - Name: faster-rcnn_r50_fpn_mdconv_c3-c5_1x_coco
    In Collection: Deformable Convolutional Networks v2
    Config: configs/dcnv2/faster-rcnn_r50-mdconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.1
      inference time (ms/im):
        - value: 56.82
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/faster_rcnn_r50_fpn_mdconv_c3-c5_1x_coco/faster_rcnn_r50_fpn_mdconv_c3-c5_1x_coco_20200130-d099253b.pth

  - Name: faster-rcnn_r50_fpn_mdconv_c3-c5_group4_1x_coco
    In Collection: Deformable Convolutional Networks v2
    Config: configs/dcnv2/faster-rcnn_r50-mdconv-group4-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.2
      inference time (ms/im):
        - value: 57.47
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/faster_rcnn_r50_fpn_mdconv_c3-c5_group4_1x_coco/faster_rcnn_r50_fpn_mdconv_c3-c5_group4_1x_coco_20200130-01262257.pth

  - Name: faster-rcnn_r50_fpn_mdpool_1x_coco
    In Collection: Deformable Convolutional Networks v2
    Config: configs/dcnv2/faster-rcnn_r50_fpn_mdpool_1x_coco.py
    Metadata:
      Training Memory (GB): 5.8
      inference time (ms/im):
        - value: 60.24
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/faster_rcnn_r50_fpn_mdpool_1x_coco/faster_rcnn_r50_fpn_mdpool_1x_coco_20200307-c0df27ff.pth

  - Name: mask-rcnn_r50_fpn_mdconv_c3-c5_1x_coco
    In Collection: Deformable Convolutional Networks v2
    Config: configs/dcnv2/mask-rcnn_r50-mdconv-c3-c5_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.5
      inference time (ms/im):
        - value: 66.23
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.5
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dcn/mask_rcnn_r50_fpn_mdconv_c3-c5_1x_coco/mask_rcnn_r50_fpn_mdconv_c3-c5_1x_coco_20200203-ad97591f.pth

  - Name: mask-rcnn_r50_fpn_fp16_mdconv_c3-c5_1x_coco
    In Collection: Deformable Convolutional Networks v2
    Config: configs/dcnv2/mask-rcnn_r50-mdconv-c3-c5_fpn_amp-1x_coco.py
    Metadata:
      Training Memory (GB): 3.1
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
        - Mixed Precision Training
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/fp16/mask_rcnn_r50_fpn_fp16_mdconv_c3-c5_1x_coco/mask_rcnn_r50_fpn_fp16_mdconv_c3-c5_1x_coco_20210520_180434-cf8fefa5.pth
