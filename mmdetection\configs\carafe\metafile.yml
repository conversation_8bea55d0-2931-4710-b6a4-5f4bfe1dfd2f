Collections:
  - Name: CA<PERSON>FE
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - RPN
        - FPN_CARAFE
        - ResNet
        - RoIPool
    Paper:
      URL: https://arxiv.org/abs/1905.02188
      Title: 'CARAFE: Content-Aware ReAssembly of FEatures'
    README: configs/carafe/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.12.0/mmdet/models/necks/fpn_carafe.py#L11
      Version: v2.12.0

Models:
  - Name: faster-rcnn_r50_fpn_carafe_1x_coco
    In Collection: CARAFE
    Config: configs/carafe/faster-rcnn_r50_fpn-carafe_1x_coco.py
    Metadata:
      Training Memory (GB): 4.26
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/carafe/faster_rcnn_r50_fpn_carafe_1x_coco/faster_rcnn_r50_fpn_carafe_1x_coco_bbox_mAP-0.386_20200504_175733-385a75b7.pth

  - Name: mask-rcnn_r50_fpn_carafe_1x_coco
    In Collection: CARAFE
    Config: configs/carafe/mask-rcnn_r50_fpn-carafe_1x_coco.py
    Metadata:
      Training Memory (GB): 4.31
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 35.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/carafe/mask_rcnn_r50_fpn_carafe_1x_coco/mask_rcnn_r50_fpn_carafe_1x_coco_bbox_mAP-0.393__segm_mAP-0.358_20200503_135957-8687f195.pth
