#!/usr/bin/env python3
"""
Mask R-CNN Object Detection Training Script using Torchvision
Supports custom COCO dataset format for object detection only.
"""

import argparse
import gc
import json
import os
import time
from pathlib import Path

import cv2
import numpy as np
import torch
import torch.utils.data
import torchvision
from PIL import Image
from torch.cuda.amp import GradScaler, autocast
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.models.detection import maskrcnn_resnet50_fpn
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
from torchvision.models.detection.mask_rcnn import MaskRCNNPredictor


class COCODataset(torch.utils.data.Dataset):
    """Custom COCO Dataset for object detection."""

    def __init__(self, root, annotation_file, transforms=None):
        self.root = Path(root)
        self.transforms = transforms

        # Load COCO annotations
        with open(annotation_file, "r") as f:
            self.coco_data = json.load(f)

        # Create mappings
        self.images = {img["id"]: img for img in self.coco_data["images"]}
        self.categories = {cat["id"]: cat for cat in self.coco_data["categories"]}

        # Group annotations by image
        self.img_annotations = {}
        for ann in self.coco_data["annotations"]:
            img_id = ann["image_id"]
            if img_id not in self.img_annotations:
                self.img_annotations[img_id] = []
            self.img_annotations[img_id].append(ann)

        # Filter images that have annotations
        self.image_ids = [img_id for img_id in self.images.keys() if img_id in self.img_annotations]

        # Create category id to label mapping (0-indexed)
        self.cat_id_to_label = {cat_id: idx for idx, cat_id in enumerate(sorted(self.categories.keys()))}
        self.num_classes = len(self.categories)

        print(f"Dataset loaded: {len(self.image_ids)} images, {self.num_classes} classes")

    def __len__(self):
        return len(self.image_ids)

    def __getitem__(self, idx):
        img_id = self.image_ids[idx]
        img_info = self.images[img_id]

        # Load image
        img_path = self.root / img_info["file_name"]
        image = Image.open(img_path).convert("RGB")

        # Get annotations for this image
        annotations = self.img_annotations[img_id]

        # Extract bounding boxes and labels
        boxes = []
        labels = []
        masks = []

        for ann in annotations:
            # Skip crowd annotations
            if ann.get("iscrowd", 0):
                continue

            # Get bounding box [x, y, width, height] -> [x1, y1, x2, y2]
            x, y, w, h = ann["bbox"]
            boxes.append([x, y, x + w, y + h])

            # Get label (convert to 0-indexed)
            cat_id = ann["category_id"]
            labels.append(self.cat_id_to_label[cat_id])

            # Create mask from segmentation (for Mask R-CNN)
            if "segmentation" in ann and ann["segmentation"]:
                mask = self._create_mask_from_segmentation(ann["segmentation"], img_info["height"], img_info["width"])
                masks.append(mask)
            else:
                # Create dummy mask from bbox if no segmentation available
                mask = np.zeros((img_info["height"], img_info["width"]), dtype=np.uint8)
                x1, y1, x2, y2 = map(int, [x, y, x + w, y + h])
                mask[y1:y2, x1:x2] = 1
                masks.append(mask)

        # Convert to tensors
        boxes = torch.as_tensor(boxes, dtype=torch.float32)
        labels = torch.as_tensor(labels, dtype=torch.int64)
        masks = torch.as_tensor(np.array(masks), dtype=torch.uint8)

        # Create target dictionary
        target = {
            "boxes": boxes,
            "labels": labels + 1,  # Add 1 because 0 is reserved for background
            "masks": masks,
            "image_id": torch.tensor([img_id]),
            "area": (boxes[:, 3] - boxes[:, 1]) * (boxes[:, 2] - boxes[:, 0]),
            "iscrowd": torch.zeros((len(boxes),), dtype=torch.int64),
        }

        if self.transforms:
            image = self.transforms(image)

        return image, target

    def _create_mask_from_segmentation(self, segmentation, height, width):
        """Create binary mask from COCO segmentation format."""
        mask = np.zeros((height, width), dtype=np.uint8)

        if isinstance(segmentation, list):
            # Polygon format
            for seg in segmentation:
                if len(seg) >= 6:  # At least 3 points
                    poly = np.array(seg).reshape(-1, 2)
                    cv2.fillPoly(mask, [poly.astype(np.int32)], (1,))
        elif isinstance(segmentation, dict):
            # RLE format (not implemented for simplicity)
            pass

        return mask


def get_transform(train=True, max_size=800):
    """Get data transforms for training/validation with memory optimization."""
    transform_list = []

    # Resize images to reduce memory usage
    if max_size:
        transform_list.append(transforms.Resize((max_size, max_size)))

    transform_list.append(transforms.ToTensor())

    return transforms.Compose(transform_list)


def collate_fn(batch):
    """Custom collate function for DataLoader."""
    return tuple(zip(*batch))


def get_gpu_memory_usage():
    """Get current GPU memory usage."""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3  # GB
        cached = torch.cuda.memory_reserved() / 1024**3  # GB
        return allocated, cached
    return 0, 0


def get_model(num_classes):
    """Create Mask R-CNN model with ResNet-50 backbone."""
    # Load pre-trained model
    model = maskrcnn_resnet50_fpn(weights="DEFAULT")

    # Get number of input features for the classifier
    in_features = model.roi_heads.box_predictor.cls_score.in_features

    # Replace the pre-trained head with a new one
    model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)

    # Get number of input features for the mask classifier
    in_features_mask = model.roi_heads.mask_predictor.conv5_mask.in_channels
    hidden_layer = 256

    # Replace the mask predictor with a new one
    model.roi_heads.mask_predictor = MaskRCNNPredictor(in_features_mask, hidden_layer, num_classes)

    return model


def train_one_epoch(model, optimizer, data_loader, device, epoch, scaler, print_freq=10):
    """Train the model for one epoch with optional AMP support and memory optimization."""
    model.train()

    running_loss = 0.0
    num_batches = len(data_loader)

    for i, (images, targets) in enumerate(data_loader):
        # Move data to device
        images = [image.to(device) for image in images]
        targets = [{k: v.to(device) for k, v in t.items()} for t in targets]

        with autocast():
            loss_dict = model(images, targets)
            losses = sum(loss for loss in loss_dict.values())

        # Backward pass with gradient scaling
        scaler.scale(losses).backward()
        scaler.step(optimizer)
        scaler.update()
        optimizer.zero_grad()

        running_loss += losses.item()  # Unscale for logging

        # Print progress
        if i % print_freq == 0:
            avg_loss = running_loss / (i + 1)
            print(f"Epoch [{epoch}], Batch [{i}/{num_batches}], Loss: {losses.item():.4f}, Avg Loss: {avg_loss:.4f}")
            # allocated, cached = get_gpu_memory_usage()
            # print(f"  GPU Memory: {allocated:.2f}GB allocated, {cached:.2f}GB cached")

            # Print individual losses
            loss_str = ", ".join([f"{k}: {v.item():.4f}" for k, v in loss_dict.items()])
            print(f"  Individual losses: {loss_str}")

    return running_loss / num_batches


def evaluate(model, data_loader, device):
    """Evaluate the model."""
    model.train()  # Keep in training mode for loss computation

    total_loss = 0.0
    num_batches = len(data_loader)

    with torch.no_grad():
        for images, targets in data_loader:
            # Move data to device
            images = [image.to(device) for image in images]
            targets = [{k: v.to(device) for k, v in t.items()} for t in targets]

            # Forward pass - model returns loss_dict in training mode
            loss_dict = model(images, targets)
            losses = sum(loss for loss in loss_dict.values())
            total_loss += losses.item()

    avg_loss = total_loss / num_batches
    print(f"Validation Loss: {avg_loss:.4f}")
    return avg_loss


def save_checkpoint(model, optimizer, epoch, loss, filepath):
    """Save model checkpoint."""
    checkpoint = {
        "epoch": epoch,
        "model_state_dict": model.state_dict(),
        "optimizer_state_dict": optimizer.state_dict(),
        "loss": loss,
    }
    torch.save(checkpoint, filepath)
    print(f"Checkpoint saved: {filepath}")


def load_checkpoint(model, optimizer, filepath, device):
    """Load model checkpoint."""
    checkpoint = torch.load(filepath, map_location=device)
    model.load_state_dict(checkpoint["model_state_dict"])
    optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
    epoch = checkpoint["epoch"]
    loss = checkpoint["loss"]
    print(f"Checkpoint loaded: {filepath}, Epoch: {epoch}, Loss: {loss:.4f}")
    return epoch, loss


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Train Mask R-CNN on custom COCO dataset")

    # Dataset arguments
    parser.add_argument(
        "--data-root", "--train-root", type=str, required=True, help="Root directory containing training images"
    )
    parser.add_argument(
        "--val-root",
        type=str,
        default=None,
        help="Root directory containing validation images (defaults to train-root)",
    )
    parser.add_argument("--train-ann", type=str, required=True, help="Path to training annotations JSON file")
    parser.add_argument("--val-ann", type=str, default=None, help="Path to validation annotations JSON file")

    # Training arguments
    parser.add_argument("--epochs", type=int, default=300, help="Number of training epochs")
    parser.add_argument(
        "--batch-size", type=int, default=2, help="Batch size for training (reduced for memory optimization)"
    )
    parser.add_argument("--lr", type=float, default=0.0001, help="Learning rate")
    parser.add_argument("--weight-decay", type=float, default=0.0005, help="Weight decay")
    parser.add_argument(
        "--num-workers", type=int, default=4, help="Number of data loading workers (reduced for memory optimization)"
    )
    parser.add_argument("--max-image-size", type=int, default=800, help="Maximum image size for memory optimization")

    # Model arguments
    parser.add_argument("--resume", type=str, default=None, help="Path to checkpoint to resume from")
    parser.add_argument("--save-dir", type=str, default="./checkpoints", help="Directory to save checkpoints")
    parser.add_argument("--save-freq", type=int, default=5, help="Save checkpoint every N epochs")

    # Device arguments
    parser.add_argument("--device", type=str, default="auto", help="Device to use (cuda, cpu, or auto)")

    args = parser.parse_args()

    torch.cuda.empty_cache()

    # Set device
    if args.device == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(args.device)
    print(f"Using device: {device}")

    # Create save directory
    os.makedirs(args.save_dir, exist_ok=True)

    # Load datasets
    print("Loading training dataset...")
    train_dataset = COCODataset(
        root=args.data_root,
        annotation_file=args.train_ann,
        transforms=get_transform(train=True, max_size=args.max_image_size),
    )

    train_loader = DataLoader(
        train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=args.num_workers, collate_fn=collate_fn
    )

    val_loader = None
    val_dataset = None
    if args.val_ann:
        print("Loading validation dataset...")
        val_root = args.val_root if args.val_root else args.data_root
        val_dataset = COCODataset(
            root=val_root,
            annotation_file=args.val_ann,
            transforms=get_transform(train=False, max_size=args.max_image_size),
        )

        val_loader = DataLoader(
            val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=args.num_workers, collate_fn=collate_fn
        )

    # Create model
    num_classes = train_dataset.num_classes + 1  # +1 for background
    print(f"Creating model with {num_classes} classes (including background)...")
    model = get_model(num_classes)
    model.to(device)

    # Create optimizer
    params = [p for p in model.parameters() if p.requires_grad]
    optimizer = torch.optim.AdamW(params, lr=args.lr, weight_decay=args.weight_decay)

    # Learning rate scheduler
    lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=3, gamma=0.1)

    # Initialize AMP scaler if using mixed precision
    scaler = GradScaler()

    # Resume from checkpoint if specified
    start_epoch = 0
    if args.resume:
        start_epoch, _ = load_checkpoint(model, optimizer, args.resume, device)
        start_epoch += 1

    print(f"Starting training for {args.epochs} epochs...")
    print(f"Training samples: {len(train_dataset)}")
    if val_loader and val_dataset:
        print(f"Validation samples: {len(val_dataset)}")

    # Memory optimization info
    print("\nMemory Optimization Settings:")
    print(f"  Batch size: {args.batch_size}")
    print(f"  Max image size: {args.max_image_size}")
    print(f"  Number of workers: {args.num_workers}")

    allocated, cached = get_gpu_memory_usage()
    print(f"  Initial GPU Memory: {allocated:.2f}GB allocated, {cached:.2f}GB cached")

    # Training loop
    best_val_loss = float("inf")
    train_loss = 0.0  # Initialize train_loss

    for epoch in range(start_epoch, args.epochs):
        print(f"\nEpoch {epoch + 1}/{args.epochs}")
        print("-" * 50)

        # Train for one epoch
        train_loss = train_one_epoch(model, optimizer, train_loader, device, epoch + 1, scaler)

        # Update learning rate
        lr_scheduler.step()

        # Validate if validation set is provided
        val_loss = None
        if val_loader:
            print("\nValidating...")
            val_loss = evaluate(model, val_loader, device)

            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_path = os.path.join(args.save_dir, "best_model.pth")
                save_checkpoint(model, optimizer, epoch, val_loss, best_path)

        # Save checkpoint periodically
        if (epoch + 1) % args.save_freq == 0:
            checkpoint_path = os.path.join(args.save_dir, f"checkpoint_epoch_{epoch + 1}.pth")
            save_checkpoint(model, optimizer, epoch, train_loss, checkpoint_path)

        print(
            f"Epoch {epoch + 1} completed - Train Loss: {train_loss:.4f}"
            + (f", Val Loss: {val_loss:.4f}" if val_loss else "")
        )

    # Save final model
    final_path = os.path.join(args.save_dir, "final_model.pth")
    save_checkpoint(model, optimizer, args.epochs - 1, train_loss, final_path)

    print("\nTraining completed!")
    print(f"Models saved in: {args.save_dir}")


if __name__ == "__main__":
    main()
