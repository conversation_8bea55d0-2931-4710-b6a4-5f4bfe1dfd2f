_base_ = "../grid_rcnn/grid-rcnn_r50_fpn_gn-head_1x_coco.py"
# model settings
model = dict(
    roi_head=dict(
        bbox_roi_extractor=dict(
            type="GenericRoIExtractor",
            aggregation="sum",
            roi_layer=dict(type="RoIAlign", output_size=7, sampling_ratio=2),
            out_channels=256,
            featmap_strides=[4, 8, 16, 32],
            pre_cfg=dict(
                type="ConvModule",
                in_channels=256,
                out_channels=256,
                kernel_size=5,
                padding=2,
                inplace=False,
            ),
            post_cfg=dict(
                type="GeneralizedAttention",
                in_channels=256,
                spatial_range=-1,
                num_heads=6,
                attention_type="0100",
                kv_stride=2,
            ),
        ),
        grid_roi_extractor=dict(
            type="GenericRoIExtractor",
            roi_layer=dict(type="RoIAlign", output_size=14, sampling_ratio=2),
            out_channels=256,
            featmap_strides=[4, 8, 16, 32],
            pre_cfg=dict(
                type="ConvModule",
                in_channels=256,
                out_channels=256,
                kernel_size=5,
                padding=2,
                inplace=False,
            ),
            post_cfg=dict(
                type="GeneralizedAttention",
                in_channels=256,
                spatial_range=-1,
                num_heads=6,
                attention_type="0100",
                kv_stride=2,
            ),
        ),
    )
)
