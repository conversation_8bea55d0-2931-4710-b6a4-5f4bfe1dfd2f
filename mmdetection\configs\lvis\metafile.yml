Models:
  - Name: mask-rcnn_r50_fpn_sample1e-3_ms-2x_lvis-v0.5
    In Collection: Mask R-CNN
    Config: configs/lvis/mask-rcnn_r50_fpn_sample1e-3_ms-2x_lvis-v0.5.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v0.5
        Metrics:
          box AP: 26.1
      - Task: Instance Segmentation
        Dataset: LVIS v0.5
        Metrics:
          mask AP: 25.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/lvis/mask_rcnn_r50_fpn_sample1e-3_mstrain_2x_lvis/mask_rcnn_r50_fpn_sample1e-3_mstrain_2x_lvis-dbd06831.pth

  - Name: mask-rcnn_r101_fpn_sample1e-3_ms-2x_lvis-v0.5
    In Collection: Mask R-CNN
    Config: configs/lvis/mask-rcnn_r101_fpn_sample1e-3_ms-2x_lvis-v0.5.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v0.5
        Metrics:
          box AP: 27.1
      - Task: Instance Segmentation
        Dataset: LVIS v0.5
        Metrics:
          mask AP: 27.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/lvis/mask_rcnn_r101_fpn_sample1e-3_mstrain_2x_lvis/mask_rcnn_r101_fpn_sample1e-3_mstrain_2x_lvis-54582ee2.pth

  - Name: mask-rcnn_x101-32x4d_fpn_sample1e-3_ms-2x_lvis-v0.5
    In Collection: Mask R-CNN
    Config: configs/lvis/mask-rcnn_x101-32x4d_fpn_sample1e-3_ms-2x_lvis-v0.5.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v0.5
        Metrics:
          box AP: 26.7
      - Task: Instance Segmentation
        Dataset: LVIS v0.5
        Metrics:
          mask AP: 26.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/lvis/mask_rcnn_x101_32x4d_fpn_sample1e-3_mstrain_2x_lvis/mask_rcnn_x101_32x4d_fpn_sample1e-3_mstrain_2x_lvis-3cf55ea2.pth

  - Name: mask-rcnn_x101-64x4d_fpn_sample1e-3_ms-2x_lvis-v0.5
    In Collection: Mask R-CNN
    Config: configs/lvis/mask-rcnn_x101-64x4d_fpn_sample1e-3_ms-2x_lvis-v0.5.py
    Metadata:
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: LVIS v0.5
        Metrics:
          box AP: 26.4
      - Task: Instance Segmentation
        Dataset: LVIS v0.5
        Metrics:
          mask AP: 26.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/lvis/mask_rcnn_x101_64x4d_fpn_sample1e-3_mstrain_2x_lvis/mask_rcnn_x101_64x4d_fpn_sample1e-3_mstrain_2x_lvis-1c99a5ad.pth

  - Name: mask-rcnn_r50_fpn_sample1e-3_ms-1x_lvis-v1
    In Collection: Mask R-CNN
    Config: configs/lvis/mask-rcnn_r50_fpn_sample1e-3_ms-1x_lvis-v1.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 22.5
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 21.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/lvis/mask_rcnn_r50_fpn_sample1e-3_mstrain_1x_lvis_v1/mask_rcnn_r50_fpn_sample1e-3_mstrain_1x_lvis_v1-aa78ac3d.pth

  - Name: mask-rcnn_r101_fpn_sample1e-3_ms-1x_lvis-v1
    In Collection: Mask R-CNN
    Config: configs/lvis/mask-rcnn_r101_fpn_sample1e-3_ms-1x_lvis-v1.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 24.6
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 23.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/lvis/mask_rcnn_r101_fpn_sample1e-3_mstrain_1x_lvis_v1/mask_rcnn_r101_fpn_sample1e-3_mstrain_1x_lvis_v1-ec55ce32.pth

  - Name: mask-rcnn_x101-32x4d_fpn_sample1e-3_ms-1x_lvis-v1
    In Collection: Mask R-CNN
    Config: configs/lvis/mask-rcnn_x101-32x4d_fpn_sample1e-3_ms-1x_lvis-v1.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 26.7
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 25.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/lvis/mask_rcnn_x101_32x4d_fpn_sample1e-3_mstrain_1x_lvis_v1/mask_rcnn_x101_32x4d_fpn_sample1e-3_mstrain_1x_lvis_v1-ebbc5c81.pth

  - Name: mask-rcnn_x101-64x4d_fpn_sample1e-3_ms-1x_lvis-v1
    In Collection: Mask R-CNN
    Config: configs/lvis/mask-rcnn_x101-64x4d_fpn_sample1e-3_ms-1x_lvis-v1.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: LVIS v1
        Metrics:
          box AP: 27.2
      - Task: Instance Segmentation
        Dataset: LVIS v1
        Metrics:
          mask AP: 25.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/lvis/mask_rcnn_x101_64x4d_fpn_sample1e-3_mstrain_1x_lvis_v1/mask_rcnn_x101_64x4d_fpn_sample1e-3_mstrain_1x_lvis_v1-43d9edfe.pth
