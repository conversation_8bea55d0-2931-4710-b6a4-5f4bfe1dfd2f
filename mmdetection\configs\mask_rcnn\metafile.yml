Collections:
  - Name: Mask R-CNN
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Softmax
        - RPN
        - Convolution
        - Dense Connections
        - FPN
        - ResNet
        - RoIAlign
    Paper:
      URL: https://arxiv.org/abs/1703.06870v3
      Title: "Mask R-CNN"
    README: configs/mask_rcnn/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/detectors/mask_rcnn.py#L6
      Version: v2.0.0

Models:
  - Name: mask-rcnn_r50-caffe_fpn_1x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r50-caffe_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.3
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 34.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r50_caffe_fpn_1x_coco/mask_rcnn_r50_caffe_fpn_1x_coco_bbox_mAP-0.38__segm_mAP-0.344_20200504_231812-0ebd1859.pth

  - Name: mask-rcnn_r50_fpn_1x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 4.4
      inference time (ms/im):
        - value: 62.11
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.2
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 34.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r50_fpn_1x_coco/mask_rcnn_r50_fpn_1x_coco_20200205-d4b0c5d6.pth

  - Name: mask-rcnn_r50_fpn_fp16_1x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r50_fpn_amp-1x_coco.py
    Metadata:
      Training Memory (GB): 3.6
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
        - Mixed Precision Training
      inference time (ms/im):
        - value: 41.49
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP16
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 34.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/fp16/mask_rcnn_r50_fpn_fp16_1x_coco/mask_rcnn_r50_fpn_fp16_1x_coco_20200205-59faf7e4.pth

  - Name: mask-rcnn_r50_fpn_2x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r50_fpn_2x_coco.py
    Metadata:
      Training Memory (GB): 4.4
      inference time (ms/im):
        - value: 62.11
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.2
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 35.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r50_fpn_2x_coco/mask_rcnn_r50_fpn_2x_coco_bbox_mAP-0.392__segm_mAP-0.354_20200505_003907-3e542a40.pth

  - Name: mask-rcnn_r101-caffe_fpn_1x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r101-caffe_fpn_1x_coco.py
    Metadata:
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.4
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r101_caffe_fpn_1x_coco/mask_rcnn_r101_caffe_fpn_1x_coco_20200601_095758-805e06c1.pth

  - Name: mask-rcnn_r101_fpn_1x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r101_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 6.4
      inference time (ms/im):
        - value: 74.07
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.0
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r101_fpn_1x_coco/mask_rcnn_r101_fpn_1x_coco_20200204-1efe0ed5.pth

  - Name: mask-rcnn_r101_fpn_2x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r101_fpn_2x_coco.py
    Metadata:
      Training Memory (GB): 6.4
      inference time (ms/im):
        - value: 74.07
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r101_fpn_2x_coco/mask_rcnn_r101_fpn_2x_coco_bbox_mAP-0.408__segm_mAP-0.366_20200505_071027-14b391c7.pth

  - Name: mask-rcnn_x101-32x4d_fpn_1x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_x101-32x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 7.6
      inference time (ms/im):
        - value: 88.5
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.9
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_x101_32x4d_fpn_1x_coco/mask_rcnn_x101_32x4d_fpn_1x_coco_20200205-478d0b67.pth

  - Name: mask-rcnn_x101-32x4d_fpn_2x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_x101-32x4d_fpn_2x_coco.py
    Metadata:
      Training Memory (GB): 7.6
      inference time (ms/im):
        - value: 88.5
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.2
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_x101_32x4d_fpn_2x_coco/mask_rcnn_x101_32x4d_fpn_2x_coco_bbox_mAP-0.422__segm_mAP-0.378_20200506_004702-faef898c.pth

  - Name: mask-rcnn_x101-64x4d_fpn_1x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_x101-64x4d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 10.7
      inference time (ms/im):
        - value: 125
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_x101_64x4d_fpn_1x_coco/mask_rcnn_x101_64x4d_fpn_1x_coco_20200201-9352eb0d.pth

  - Name: mask-rcnn_x101-64x4d_fpn_2x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_x101-64x4d_fpn_2x_coco.py
    Metadata:
      Training Memory (GB): 10.7
      inference time (ms/im):
        - value: 125
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.7
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_x101_64x4d_fpn_2x_coco/mask_rcnn_x101_64x4d_fpn_2x_coco_20200509_224208-39d6f70c.pth

  - Name: mask-rcnn_x101-32x8d_fpn_1x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_x101-32x8d_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 10.6
      Epochs: 12
    Results:
    - Task: Object Detection
      Dataset: COCO
      Metrics:
        box AP: 42.8
    - Task: Instance Segmentation
      Dataset: COCO
      Metrics:
        mask AP: 38.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_x101_32x8d_fpn_1x_coco/mask_rcnn_x101_32x8d_fpn_1x_coco_20220630_173841-0aaf329e.pth

  - Name: mask-rcnn_r50-caffe_fpn_ms-poly-2x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r50-caffe_fpn_ms-poly-2x_coco.py
    Metadata:
      Training Memory (GB): 4.3
      Epochs: 24
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r50_caffe_fpn_mstrain-poly_2x_coco/mask_rcnn_r50_caffe_fpn_mstrain-poly_2x_coco_bbox_mAP-0.403__segm_mAP-0.365_20200504_231822-a75c98ce.pth

  - Name: mask-rcnn_r50-caffe_fpn_ms-poly-3x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r50-caffe_fpn_ms-poly-3x_coco.py
    Metadata:
      Training Memory (GB): 4.3
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r50_caffe_fpn_mstrain-poly_3x_coco/mask_rcnn_r50_caffe_fpn_mstrain-poly_3x_coco_bbox_mAP-0.408__segm_mAP-0.37_20200504_163245-42aa3d00.pth

  - Name: mask-rcnn_r50_fpn_mstrain-poly_3x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r50_fpn_ms-poly-3x_coco.py
    Metadata:
      Training Memory (GB): 4.1
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.9
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r50_fpn_mstrain-poly_3x_coco/mask_rcnn_r50_fpn_mstrain-poly_3x_coco_20210524_201154-21b550bb.pth

  - Name: mask-rcnn_r101_fpn_ms-poly-3x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r101_fpn_ms-poly-3x_coco.py
    Metadata:
      Training Memory (GB): 6.1
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.7
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r101_fpn_mstrain-poly_3x_coco/mask_rcnn_r101_fpn_mstrain-poly_3x_coco_20210524_200244-5675c317.pth

  - Name: mask-rcnn_r101-caffe_fpn_ms-poly-3x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_r101-caffe_fpn_ms-poly-3x_coco.py
    Metadata:
      Training Memory (GB): 5.9
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.9
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_r101_caffe_fpn_mstrain-poly_3x_coco/mask_rcnn_r101_caffe_fpn_mstrain-poly_3x_coco_20210526_132339-3c33ce02.pth

  - Name: mask-rcnn_x101-32x4d_fpn_ms-poly-3x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_x101-32x4d_fpn_ms-poly-3x_coco.py
    Metadata:
      Training Memory (GB): 7.3
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_x101_32x4d_fpn_mstrain-poly_3x_coco/mask_rcnn_x101_32x4d_fpn_mstrain-poly_3x_coco_20210524_201410-abcd7859.pth

  - Name: mask-rcnn_x101-32x8d_fpn_ms-poly-1x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_x101-32x8d_fpn_ms-poly-1x_coco.py
    Metadata:
      Training Memory (GB): 10.4
      Epochs: 12
    Results:
    - Task: Object Detection
      Dataset: COCO
      Metrics:
        box AP: 43.4
    - Task: Instance Segmentation
      Dataset: COCO
      Metrics:
        mask AP: 39.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_x101_32x8d_fpn_mstrain-poly_1x_coco/mask_rcnn_x101_32x8d_fpn_mstrain-poly_1x_coco_20220630_170346-b4637974.pth

  - Name: mask-rcnn_x101-32x8d_fpn_ms-poly-3x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_x101-32x8d_fpn_ms-poly-3x_coco.py
    Metadata:
      Training Memory (GB): 10.3
      Epochs: 36
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_x101_32x8d_fpn_mstrain-poly_3x_coco/mask_rcnn_x101_32x8d_fpn_mstrain-poly_3x_coco_20210607_161042-8bd2c639.pth

  - Name: mask-rcnn_x101-64x4d_fpn_ms-poly_3x_coco
    In Collection: Mask R-CNN
    Config: configs/mask_rcnn/mask-rcnn_x101-64x4d_fpn_ms-poly_3x_coco.py
    Metadata:
      Epochs: 36
      Training Memory (GB): 10.4
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.5
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/mask_rcnn/mask_rcnn_x101_64x4d_fpn_mstrain-poly_3x_coco/mask_rcnn_x101_64x4d_fpn_mstrain-poly_3x_coco_20210526_120447-c376f129.pth
