Collections:
  - Name: Dynamic R-CNN
    Metadata:
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - Dynamic R-CNN
        - FPN
        - RPN
        - ResNet
        - RoIAlign
    Paper:
      URL: https://arxiv.org/pdf/2004.06002
      Title: 'Dynamic R-CNN: Towards High Quality Object Detection via Dynamic Training'
    README: configs/dynamic_rcnn/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.2.0/mmdet/models/roi_heads/dynamic_roi_head.py#L11
      Version: v2.2.0

Models:
  - Name: dynamic-rcnn_r50_fpn_1x_coco
    In Collection: Dynamic R-CNN
    Config: configs/dynamic_rcnn/dynamic-rcnn_r50_fpn_1x_coco.py
    Metadata:
      Training Memory (GB): 3.8
      Epochs: 12
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/dynamic_rcnn/dynamic_rcnn_r50_fpn_1x/dynamic_rcnn_r50_fpn_1x-62a3f276.pth
