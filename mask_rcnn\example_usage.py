#!/usr/bin/env python3
"""
Example usage script for Mask R-CNN training.
This script demonstrates how to prepare data and train the model.
"""

import json
import os
from pathlib import Path


def create_example_coco_annotation():
    """Create an example COCO annotation file for demonstration."""
    
    # Example COCO format annotation
    coco_data = {
        "images": [
            {
                "id": 1,
                "file_name": "example1.jpg",
                "width": 640,
                "height": 480
            },
            {
                "id": 2,
                "file_name": "example2.jpg", 
                "width": 640,
                "height": 480
            }
        ],
        "annotations": [
            {
                "id": 1,
                "image_id": 1,
                "category_id": 1,
                "bbox": [100, 100, 200, 150],  # [x, y, width, height]
                "area": 30000,
                "iscrowd": 0,
                "segmentation": [[100, 100, 300, 100, 300, 250, 100, 250]]  # polygon
            },
            {
                "id": 2,
                "image_id": 2,
                "category_id": 1,
                "bbox": [50, 50, 100, 100],
                "area": 10000,
                "iscrowd": 0,
                "segmentation": [[50, 50, 150, 50, 150, 150, 50, 150]]
            }
        ],
        "categories": [
            {
                "id": 1,
                "name": "tobacco",
                "supercategory": "object"
            }
        ]
    }
    
    return coco_data


def main():
    """Main function to demonstrate usage."""
    
    print("Mask R-CNN Training Example")
    print("=" * 40)
    
    # Create example annotation file
    example_ann = create_example_coco_annotation()
    
    # Save example annotation
    os.makedirs("example_data", exist_ok=True)
    with open("example_data/train_annotations.json", "w") as f:
        json.dump(example_ann, f, indent=2)
    
    print("Created example annotation file: example_data/train_annotations.json")
    
    # Print example training command
    print("\nExample training command:")
    print("-" * 25)
    
    train_cmd = """python train.py \\
    --data-root example_data/images \\
    --train-ann example_data/train_annotations.json \\
    --epochs 10 \\
    --batch-size 2 \\
    --lr 0.005 \\
    --save-dir ./checkpoints \\
    --device auto"""
    
    print(train_cmd)
    
    print("\nExample training with validation:")
    print("-" * 32)
    
    train_val_cmd = """python train.py \\
    --data-root example_data/images \\
    --train-ann example_data/train_annotations.json \\
    --val-ann example_data/val_annotations.json \\
    --epochs 20 \\
    --batch-size 4 \\
    --lr 0.005 \\
    --momentum 0.9 \\
    --weight-decay 0.0005 \\
    --save-dir ./checkpoints \\
    --save-freq 5 \\
    --num-workers 4"""
    
    print(train_val_cmd)
    
    print("\nExample resume training:")
    print("-" * 23)
    
    resume_cmd = """python train.py \\
    --data-root example_data/images \\
    --train-ann example_data/train_annotations.json \\
    --resume ./checkpoints/checkpoint_epoch_10.pth \\
    --epochs 20 \\
    --batch-size 2"""
    
    print(resume_cmd)
    
    print("\nDataset structure should be:")
    print("-" * 28)
    
    structure = """example_data/
├── images/
│   ├── example1.jpg
│   ├── example2.jpg
│   └── ...
├── train_annotations.json
└── val_annotations.json (optional)"""
    
    print(structure)
    
    print("\nNotes:")
    print("- Make sure your images are in the images/ directory")
    print("- Annotation file paths in JSON should match actual image files")
    print("- The script will automatically detect GPU if available")
    print("- Reduce batch size if you encounter out-of-memory errors")
    print("- Check the README.md for more detailed information")


if __name__ == "__main__":
    main()
