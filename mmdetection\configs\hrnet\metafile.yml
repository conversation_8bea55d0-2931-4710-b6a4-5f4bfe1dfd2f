Models:
  - Name: faster-rcnn_hrnetv2p-w18-1x_coco
    In Collection: Faster R-CNN
    Config: configs/hrnet/faster-rcnn_hrnetv2p-w18-1x_coco.py
    Metadata:
      Training Memory (GB): 6.6
      inference time (ms/im):
        - value: 74.63
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 36.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/faster_rcnn_hrnetv2p_w18_1x_coco/faster_rcnn_hrnetv2p_w18_1x_coco_20200130-56651a6d.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: faster-rcnn_hrnetv2p-w18-2x_coco
    In Collection: Faster R-CNN
    Config: configs/hrnet/faster-rcnn_hrnetv2p-w18-2x_coco.py
    Metadata:
      Training Memory (GB): 6.6
      inference time (ms/im):
        - value: 74.63
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/faster_rcnn_hrnetv2p_w18_2x_coco/faster_rcnn_hrnetv2p_w18_2x_coco_20200702_085731-a4ec0611.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: faster-rcnn_hrnetv2p-w32-1x_coco
    In Collection: Faster R-CNN
    Config: configs/hrnet/faster-rcnn_hrnetv2p-w32-1x_coco.py
    Metadata:
      Training Memory (GB): 9.0
      inference time (ms/im):
        - value: 80.65
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.2
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/faster_rcnn_hrnetv2p_w32_1x_coco/faster_rcnn_hrnetv2p_w32_1x_coco_20200130-6e286425.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: faster-rcnn_hrnetv2p-w32_2x_coco
    In Collection: Faster R-CNN
    Config: configs/hrnet/faster-rcnn_hrnetv2p-w32_2x_coco.py
    Metadata:
      Training Memory (GB): 9.0
      inference time (ms/im):
        - value: 80.65
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/faster_rcnn_hrnetv2p_w32_2x_coco/faster_rcnn_hrnetv2p_w32_2x_coco_20200529_015927-976a9c15.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: faster-rcnn_hrnetv2p-w40-1x_coco
    In Collection: Faster R-CNN
    Config: configs/hrnet/faster-rcnn_hrnetv2p-w40-1x_coco.py
    Metadata:
      Training Memory (GB): 10.4
      inference time (ms/im):
        - value: 95.24
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.2
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/faster_rcnn_hrnetv2p_w40_1x_coco/faster_rcnn_hrnetv2p_w40_1x_coco_20200210-95c1f5ce.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: faster-rcnn_hrnetv2p-w40_2x_coco
    In Collection: Faster R-CNN
    Config: configs/hrnet/faster-rcnn_hrnetv2p-w40_2x_coco.py
    Metadata:
      Training Memory (GB): 10.4
      inference time (ms/im):
        - value: 95.24
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/faster_rcnn_hrnetv2p_w40_2x_coco/faster_rcnn_hrnetv2p_w40_2x_coco_20200512_161033-0f236ef4.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: mask-rcnn_hrnetv2p-w18-1x_coco
    In Collection: Mask R-CNN
    Config: configs/hrnet/mask-rcnn_hrnetv2p-w18-1x_coco.py
    Metadata:
      Training Memory (GB): 7.0
      inference time (ms/im):
        - value: 85.47
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 37.7
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 34.2
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/mask_rcnn_hrnetv2p_w18_1x_coco/mask_rcnn_hrnetv2p_w18_1x_coco_20200205-1c3d78ed.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: mask-rcnn_hrnetv2p-w18-2x_coco
    In Collection: Mask R-CNN
    Config: configs/hrnet/mask-rcnn_hrnetv2p-w18-2x_coco.py
    Metadata:
      Training Memory (GB): 7.0
      inference time (ms/im):
        - value: 85.47
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.0
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/mask_rcnn_hrnetv2p_w18_2x_coco/mask_rcnn_hrnetv2p_w18_2x_coco_20200212-b3c825b1.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: mask-rcnn_hrnetv2p-w32-1x_coco
    In Collection: Mask R-CNN
    Config: configs/hrnet/mask-rcnn_hrnetv2p-w32-1x_coco.py
    Metadata:
      Training Memory (GB): 9.4
      inference time (ms/im):
        - value: 88.5
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.2
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.1
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/mask_rcnn_hrnetv2p_w32_1x_coco/mask_rcnn_hrnetv2p_w32_1x_coco_20200207-b29f616e.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: mask-rcnn_hrnetv2p-w32-2x_coco
    In Collection: Mask R-CNN
    Config: configs/hrnet/mask-rcnn_hrnetv2p-w32-2x_coco.py
    Metadata:
      Training Memory (GB): 9.4
      inference time (ms/im):
        - value: 88.5
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.5
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/mask_rcnn_hrnetv2p_w32_2x_coco/mask_rcnn_hrnetv2p_w32_2x_coco_20200213-45b75b4d.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: mask-rcnn_hrnetv2p-w40_1x_coco
    In Collection: Mask R-CNN
    Config: configs/hrnet/mask-rcnn_hrnetv2p-w40_1x_coco.py
    Metadata:
      Training Memory (GB): 10.9
      Epochs: 12
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/mask_rcnn_hrnetv2p_w40_1x_coco/mask_rcnn_hrnetv2p_w40_1x_coco_20200511_015646-66738b35.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: mask-rcnn_hrnetv2p-w40-2x_coco
    In Collection: Mask R-CNN
    Config: configs/hrnet/mask-rcnn_hrnetv2p-w40-2x_coco.py
    Metadata:
      Training Memory (GB): 10.9
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.2
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/mask_rcnn_hrnetv2p_w40_2x_coco/mask_rcnn_hrnetv2p_w40_2x_coco_20200512_163732-aed5e4ab.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: cascade-rcnn_hrnetv2p-w18-20e_coco
    In Collection: Cascade R-CNN
    Config: configs/hrnet/cascade-rcnn_hrnetv2p-w18-20e_coco.py
    Metadata:
      Training Memory (GB): 7.0
      inference time (ms/im):
        - value: 90.91
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.2
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/cascade_rcnn_hrnetv2p_w18_20e_coco/cascade_rcnn_hrnetv2p_w18_20e_coco_20200210-434be9d7.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: cascade-rcnn_hrnetv2p-w32-20e_coco
    In Collection: Cascade R-CNN
    Config: configs/hrnet/cascade-rcnn_hrnetv2p-w32-20e_coco.py
    Metadata:
      Training Memory (GB): 9.4
      inference time (ms/im):
        - value: 90.91
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/cascade_rcnn_hrnetv2p_w32_20e_coco/cascade_rcnn_hrnetv2p_w32_20e_coco_20200208-928455a4.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: cascade-rcnn_hrnetv2p-w40-20e_coco
    In Collection: Cascade R-CNN
    Config: configs/hrnet/cascade-rcnn_hrnetv2p-w40-20e_coco.py
    Metadata:
      Training Memory (GB): 10.8
      Epochs: 20
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 43.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/cascade_rcnn_hrnetv2p_w40_20e_coco/cascade_rcnn_hrnetv2p_w40_20e_coco_20200512_161112-75e47b04.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: cascade-mask-rcnn_hrnetv2p-w18_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/hrnet/cascade-mask-rcnn_hrnetv2p-w18_20e_coco.py
    Metadata:
      Training Memory (GB): 8.5
      inference time (ms/im):
        - value: 117.65
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.6
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 36.4
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/cascade_mask_rcnn_hrnetv2p_w18_20e_coco/cascade_mask_rcnn_hrnetv2p_w18_20e_coco_20200210-b543cd2b.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: cascade-mask-rcnn_hrnetv2p-w32_20e_coco
    In Collection: Cascade R-CNN
    Config: configs/hrnet/cascade-mask-rcnn_hrnetv2p-w32_20e_coco.py
    Metadata:
      inference time (ms/im):
        - value: 120.48
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 44.3
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 38.6
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/cascade_mask_rcnn_hrnetv2p_w32_20e_coco/cascade_mask_rcnn_hrnetv2p_w32_20e_coco_20200512_154043-39d9cf7b.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: cascade-mask-rcnn_hrnetv2p-w40-20e_coco
    In Collection: Cascade R-CNN
    Config: configs/hrnet/cascade-mask-rcnn_hrnetv2p-w40-20e_coco.py
    Metadata:
      Training Memory (GB): 12.5
      Epochs: 20
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.1
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/cascade_mask_rcnn_hrnetv2p_w40_20e_coco/cascade_mask_rcnn_hrnetv2p_w40_20e_coco_20200527_204922-969c4610.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: htc_hrnetv2p-w18_20e_coco
    In Collection: HTC
    Config: configs/hrnet/htc_hrnetv2p-w18_20e_coco.py
    Metadata:
      Training Memory (GB): 10.8
      inference time (ms/im):
        - value: 212.77
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.8
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 37.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/htc_hrnetv2p_w18_20e_coco/htc_hrnetv2p_w18_20e_coco_20200210-b266988c.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: htc_hrnetv2p-w32_20e_coco
    In Collection: HTC
    Config: configs/hrnet/htc_hrnetv2p-w32_20e_coco.py
    Metadata:
      Training Memory (GB): 13.1
      inference time (ms/im):
        - value: 204.08
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 20
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 45.4
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 39.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/htc_hrnetv2p_w32_20e_coco/htc_hrnetv2p_w32_20e_coco_20200207-7639fa12.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: htc_hrnetv2p-w40_20e_coco
    In Collection: HTC
    Config: configs/hrnet/htc_hrnetv2p-w40_20e_coco.py
    Metadata:
      Training Memory (GB): 14.6
      Epochs: 20
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Training Resources: 8x V100 GPUs
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 46.4
      - Task: Instance Segmentation
        Dataset: COCO
        Metrics:
          mask AP: 40.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/htc_hrnetv2p_w40_20e_coco/htc_hrnetv2p_w40_20e_coco_20200529_183411-417c4d5b.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: fcos_hrnetv2p-w18-gn-head_4xb4-1x_coco
    In Collection: FCOS
    Config: configs/hrnet/fcos_hrnetv2p-w18-gn-head_4xb4-1x_coco.py
    Metadata:
      Training Resources: 4x V100 GPUs
      Batch Size: 16
      Training Memory (GB): 13.0
      inference time (ms/im):
        - value: 77.52
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 35.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/fcos_hrnetv2p_w18_gn-head_4x4_1x_coco/fcos_hrnetv2p_w18_gn-head_4x4_1x_coco_20201212_100710-4ad151de.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: fcos_hrnetv2p-w18-gn-head_4xb4-2x_coco
    In Collection: FCOS
    Config: configs/hrnet/fcos_hrnetv2p-w18-gn-head_4xb4-2x_coco.py
    Metadata:
      Training Resources: 4x V100 GPUs
      Batch Size: 16
      Training Memory (GB): 13.0
      inference time (ms/im):
        - value: 77.52
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.2
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/fcos_hrnetv2p_w18_gn-head_4x4_2x_coco/fcos_hrnetv2p_w18_gn-head_4x4_2x_coco_20201212_101110-5c575fa5.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: fcos_hrnetv2p-w32-gn-head_4xb4-1x_coco
    In Collection: FCOS
    Config: configs/hrnet/fcos_hrnetv2p-w32-gn-head_4xb4-1x_coco.py
    Metadata:
      Training Resources: 4x V100 GPUs
      Batch Size: 16
      Training Memory (GB): 17.5
      inference time (ms/im):
        - value: 77.52
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 12
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 39.5
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/fcos_hrnetv2p_w32_gn-head_4x4_1x_coco/fcos_hrnetv2p_w32_gn-head_4x4_1x_coco_20201211_134730-cb8055c0.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: fcos_hrnetv2p-w32-gn-head_4xb4-2x_coco
    In Collection: FCOS
    Config: configs/hrnet/fcos_hrnetv2p-w32-gn-head_4xb4-2x_coco.py
    Metadata:
      Training Resources: 4x V100 GPUs
      Batch Size: 16
      Training Memory (GB): 17.5
      inference time (ms/im):
        - value: 77.52
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 40.8
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/fcos_hrnetv2p_w32_gn-head_4x4_2x_coco/fcos_hrnetv2p_w32_gn-head_4x4_2x_coco_20201212_112133-77b6b9bb.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: fcos_hrnetv2p-w18-gn-head_ms-640-800-4xb4-2x_coco
    In Collection: FCOS
    Config: configs/hrnet/fcos_hrnetv2p-w18-gn-head_ms-640-800-4xb4-2x_coco.py
    Metadata:
      Training Resources: 4x V100 GPUs
      Batch Size: 16
      Training Memory (GB): 13.0
      inference time (ms/im):
        - value: 77.52
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 38.3
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/fcos_hrnetv2p_w18_gn-head_mstrain_640-800_4x4_2x_coco/fcos_hrnetv2p_w18_gn-head_mstrain_640-800_4x4_2x_coco_20201212_111651-441e9d9f.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: fcos_hrnetv2p-w32-gn-head_ms-640-800-4xb4-2x_coco
    In Collection: FCOS
    Config: configs/hrnet/fcos_hrnetv2p-w32-gn-head_ms-640-800-4xb4-2x_coco.py
    Metadata:
      Training Resources: 4x V100 GPUs
      Batch Size: 16
      Training Memory (GB): 17.5
      inference time (ms/im):
        - value: 80.65
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 41.9
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/fcos_hrnetv2p_w32_gn-head_mstrain_640-800_4x4_2x_coco/fcos_hrnetv2p_w32_gn-head_mstrain_640-800_4x4_2x_coco_20201212_090846-b6f2b49f.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0

  - Name: fcos_hrnetv2p-w40-gn-head_ms-640-800-4xb4-2x_coco
    In Collection: FCOS
    Config: configs/hrnet/fcos_hrnetv2p-w40-gn-head_ms-640-800-4xb4-2x_coco.py
    Metadata:
      Training Resources: 4x V100 GPUs
      Batch Size: 16
      Training Memory (GB): 20.3
      inference time (ms/im):
        - value: 92.59
          hardware: V100
          backend: PyTorch
          batch size: 1
          mode: FP32
          resolution: (800, 1333)
      Epochs: 24
      Training Data: COCO
      Training Techniques:
        - SGD with Momentum
        - Weight Decay
      Architecture:
        - HRNet
    Results:
      - Task: Object Detection
        Dataset: COCO
        Metrics:
          box AP: 42.7
    Weights: https://download.openmmlab.com/mmdetection/v2.0/hrnet/fcos_hrnetv2p_w40_gn-head_mstrain_640-800_4x4_2x_coco/fcos_hrnetv2p_w40_gn-head_mstrain_640-800_4x4_2x_coco_20201212_124752-f22d2ce5.pth
    Paper:
      URL: https://arxiv.org/abs/1904.04514
      Title: 'Deep High-Resolution Representation Learning for Visual Recognition'
    README: configs/hrnet/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection/blob/v2.0.0/mmdet/models/backbones/hrnet.py#L195
      Version: v2.0.0
