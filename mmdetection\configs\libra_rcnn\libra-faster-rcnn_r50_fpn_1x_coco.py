_base_ = "../faster_rcnn/faster-rcnn_r50_fpn_1x_coco.py"
# model settings
model = dict(
    neck=[
        dict(type="FPN", in_channels=[256, 512, 1024, 2048], out_channels=256, num_outs=5),
        dict(type="BFP", in_channels=256, num_levels=5, refine_level=2, refine_type="non_local"),
    ],
    roi_head=dict(
        bbox_head=dict(
            loss_bbox=dict(
                _delete_=True,
                type="BalancedL1Loss",
                alpha=0.5,
                gamma=1.5,
                beta=1.0,
                loss_weight=1.0,
            )
        )
    ),
    # model training and testing settings
    train_cfg=dict(
        rpn=dict(sampler=dict(neg_pos_ub=5), allowed_border=-1),
        rcnn=dict(
            sampler=dict(
                _delete_=True,
                type="CombinedSampler",
                num=512,
                pos_fraction=0.25,
                add_gt_as_proposals=True,
                pos_sampler=dict(type="InstanceBalancedPosSampler"),
                neg_sampler=dict(
                    type="IoUBalancedNegSampler", floor_thr=-1, floor_fraction=0, num_bins=3
                ),
            )
        ),
    ),
)
